import { RewardMechanics, <PERSON><PERSON>Roll, GameRoundResult, GameEndData, RewardDefinition, RewardHistory, RewardPool } from './types';

const rewardRolls: Map<string, RewardRoll> = new Map();
 const rewardHistory: RewardHistory = {
      rewardPoolId: `pool-1`,
      rolls: [],
    };
 const activeRounds: Map<string, { startTime: number }> = new Map();


export class RewardsManager {
  private gameWidgetId: string;
  private rewardMechanics: RewardMechanics[];
  
  // State storage - will be replaced with backend API calls later

  
  // Reward pool - will be replaced with backend API calls later
  private rewardPool: RewardPool | null = null;

  constructor(gameWidgetId: string, rewardMechanics: RewardMechanics[]) {
    this.gameWidgetId = gameWidgetId;
    this.rewardMechanics = rewardMechanics;

    //this is just to mock!
    rewardHistory.rewardPoolId = `pool-${gameWidgetId}`;
  }

  // Round Management
  public startRound(roundId: string): string {
    activeRounds.set(roundId, {
      startTime: Date.now(),
    });

    const applicableMechanics = this.rewardMechanics.filter(m =>
      m.triggerOn === 'round_start'
    );

    // Trigger reward rolling asynchronously for round_start mechanics
    if (applicableMechanics.length > 0 && this.shouldTriggerReward({}, 'round_start')) {
      // Roll reward asynchronously without blocking
      this.rollReward(roundId).catch(error => {
        console.error('Error rolling reward for round start:', error);
      });
    }

    return roundId;
  }

  public async finishRound(
    roundId: string,
    result: GameRoundResult
  ): Promise<RewardRoll | null> {
    const roundInfo = activeRounds.get(roundId);
    if (!roundInfo) {
      throw new Error(`Round ${roundId} not found`);
    }

    const applicableMechanics = this.rewardMechanics.filter(m => 
      m.triggerOn === 'round_finish'
    );

    if (applicableMechanics.length > 0 && this.shouldTriggerReward(result, 'round_finish')) {
      const rewardRoll = await this.rollReward(roundId);
      return rewardRoll;
    }

    activeRounds.delete(roundId);
    return null;
  }

  public async finishGame(finalData: GameEndData): Promise<void> {
    const applicableMechanics = this.rewardMechanics.filter(m => 
      m.triggerOn === 'game_finish'
    );

    if (applicableMechanics.length > 0 && this.shouldTriggerReward({ score: finalData.finalScore }, 'game_finish')) {
      const roundId = `game_finish_${this.gameWidgetId}_${Date.now()}`;
      await this.rollReward(roundId);
    }
  }

  // Unified reward trigger logic
  private shouldTriggerReward(data: { score?: number }, triggerType?: 'round_start' | 'round_finish' | 'game_finish'): boolean {
    // For round_start, use different logic since no score is available yet
    if (triggerType === 'round_start') {
      // Simple logic for round start: 30% chance to trigger
      return Math.random() < 0.3;
    }

    // For round_finish and game_finish, use score-based logic
    const score = data.score || 0;
    return score > 10;
  }

  // Reward Rolling
  private async rollReward(roundId: string): Promise<RewardRoll> {
    const rollId = `roll_${roundId}_${Date.now()}`;
    
    const rewardRoll: RewardRoll = {
      id: rollId,
      roundId,
      gameWidgetId: this.gameWidgetId,
      timestamp: Date.now(),
      status: 'rolling',
    };

    rewardRolls.set(rollId, rewardRoll);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));

      // Simple win logic: 50% chance to win
      const hasWon = Math.random() < 0.5;

      let reward: RewardDefinition | undefined;
      if (hasWon) {
        const rewardPool = await this.getRewardPool();
        reward = this.selectRandomReward(rewardPool.rewards);
      }

      rewardRoll.result = {
        hasWon,
        reward,
      };
      rewardRoll.status = 'completed';

    } catch (error) {
      rewardRoll.status = 'failed';
    }

    rewardRolls.set(rollId, rewardRoll);
    rewardHistory.rolls.push(rewardRoll);
    return rewardRoll;
  }

  // Reward Pool Management (will be replaced with backend API calls later)
  private async getRewardPool(): Promise<RewardPool> {
    if (!this.rewardPool) {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));
      this.rewardPool = this.getMockRewardPool();
    }
    
    return this.rewardPool;
  }

  private selectRandomReward(rewards: RewardDefinition[]): RewardDefinition | null {
    if (rewards.length === 0) return null;
    const randomIndex = Math.floor(Math.random() * rewards.length);
    return rewards[randomIndex];
  }

  // Mock data - will be replaced with backend API calls later
  private getMockRewardPool(): RewardPool {
    return {
      id: `pool-${this.gameWidgetId}`,
      name: `Reward Pool for ${this.gameWidgetId}`,
      rewards: [
        {
          id: `reward-1-${this.gameWidgetId}`,
          name: "50% Off Coupon",
          description: "Get 50% off your next purchase",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF',
          },
        },
        {
          id: `reward-2-${this.gameWidgetId}`,
          name: "Free Shipping",
          description: "Free shipping on your next order",
          type: "claimable-url",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/0000ff/ffffff?text=SHIP',
          },
        },
        {
          id: `reward-3-${this.gameWidgetId}`,
          name: "100% Off - Epic!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
      ]
    };
  }

  // State Access - will be replaced with backend API calls later
  public async getRewardByRoundId(roundId: string): Promise<RewardDefinition | null> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log("Rolls: ", rewardRolls)
    
    for (const roll of Object.values(rewardRolls)) {
      if (roll.roundId === roundId && roll.result?.hasWon) {
        return roll.result.reward || null;
      }
    }
    return null;
  }

  public async getRewardHistory(): Promise<RewardHistory> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return rewardHistory;
  }
}

