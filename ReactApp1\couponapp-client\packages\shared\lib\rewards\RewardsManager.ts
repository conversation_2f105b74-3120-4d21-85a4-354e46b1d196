import { RewardMechanics, <PERSON>wardRoll, RewardDefinition, RewardHistory, RewardPool } from './types';

const rewardRolls: Map<string, RewardRoll> = new Map();
 const rewardHistory: RewardHistory = {
      rewardPoolId: `pool-1`,
      rolls: [],
    };
 const activeRounds: Map<string, { startTime: number }> = new Map();


export class RewardsManager {
  private gameWidgetId: string;
  private rewardMechanics: RewardMechanics[];
  
  // State storage - will be replaced with backend API calls later

  
  // Reward pool - will be replaced with backend API calls later
  private rewardPool: RewardPool | null = null;

  constructor(gameWidgetId: string, rewardMechanics: RewardMechanics[]) {
    this.gameWidgetId = gameWidgetId;
    this.rewardMechanics = rewardMechanics;

    //this is just to mock!
    rewardHistory.rewardPoolId = `pool-${gameWidgetId}`;
  }

  // Unified Event Handler
  public async handleGameEvent(
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    roundId: string,
    data: { score?: number } = {}
  ): Promise<RewardRoll | null> {
    // Handle round tracking
    if (triggerType === 'round_start') {
      activeRounds.set(roundId, {
        startTime: Date.now(),
      });
    } else if (triggerType === 'round_finish') {
      const roundInfo = activeRounds.get(roundId);
      if (!roundInfo) {
        throw new Error(`Round ${roundId} not found`);
      }
      activeRounds.delete(roundId);
    }

    const applicableMechanics = this.rewardMechanics.filter(m =>
      m.triggerOn === triggerType
    );

    // Check if we should respond to this event
    if (applicableMechanics.length > 0 && this.shouldTriggerReward(data, triggerType)) {
      // For game_finish, generate a unique round ID if not provided
      const effectiveRoundId = triggerType === 'game_finish'
        ? `game_finish_${this.gameWidgetId}_${Date.now()}`
        : roundId;

      const rewardRoll = await this.rollReward(effectiveRoundId);
      return rewardRoll;
    }

    return null;
  }

  // Check if we should respond to this event (not win logic)
  private shouldTriggerReward(_data: { score?: number }, _triggerType: 'round_start' | 'round_finish' | 'game_finish'): boolean {
    // This method only determines if we should respond to the event
    // Win logic is handled in rollReward method

    // For now, always respond to events if mechanics are configured
    // In the future, this could include conditions like:
    // - Cooldown periods
    // - Maximum attempts per day
    // - User eligibility checks
    // - etc.

    return true;
  }

  // Reward Rolling
  private async rollReward(roundId: string): Promise<RewardRoll> {
    const rollId = `roll_${roundId}_${Date.now()}`;
    
    const rewardRoll: RewardRoll = {
      id: rollId,
      roundId,
      gameWidgetId: this.gameWidgetId,
      timestamp: Date.now(),
      status: 'rolling',
    };

    rewardRolls.set(rollId, rewardRoll);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));

      // Simple win logic: 50% chance to win
      const hasWon = Math.random() < 0.5;

      let reward: RewardDefinition | undefined;
      if (hasWon) {
        const rewardPool = await this.getRewardPool();
        reward = this.selectRandomReward(rewardPool.rewards);
      }

      rewardRoll.result = {
        hasWon,
        reward,
      };
      rewardRoll.status = 'completed';

    } catch (error) {
      rewardRoll.status = 'failed';
    }

    rewardRolls.set(rollId, rewardRoll);
    rewardHistory.rolls.push(rewardRoll);
    return rewardRoll;
  }

  // Reward Pool Management (will be replaced with backend API calls later)
  private async getRewardPool(): Promise<RewardPool> {
    if (!this.rewardPool) {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));
      this.rewardPool = this.getMockRewardPool();
    }
    
    return this.rewardPool;
  }

  private selectRandomReward(rewards: RewardDefinition[]): RewardDefinition | null {
    if (rewards.length === 0) return null;
    const randomIndex = Math.floor(Math.random() * rewards.length);
    return rewards[randomIndex];
  }

  // Mock data - will be replaced with backend API calls later
  private getMockRewardPool(): RewardPool {
    return {
      id: `pool-${this.gameWidgetId}`,
      name: `Reward Pool for ${this.gameWidgetId}`,
      rewards: [
        {
          id: `reward-1-${this.gameWidgetId}`,
          name: "50% Off Coupon",
          description: "Get 50% off your next purchase",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/00ff00/ffffff?text=50OFF',
          },
        },
        {
          id: `reward-2-${this.gameWidgetId}`,
          name: "Free Shipping",
          description: "Free shipping on your next order",
          type: "claimable-url",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/0000ff/ffffff?text=SHIP',
          },
        },
        {
          id: `reward-3-${this.gameWidgetId}`,
          name: "100% Off - Epic!",
          description: "Complete discount on selected items",
          type: "coupon-code",
          image: {
            absoluteUrl: 'https://placehold.co/100x100/ff0000/ffffff?text=100OFF',
          },
        },
      ]
    };
  }

  // State Access - will be replaced with backend API calls later
  public async getRewardByRoundId(roundId: string): Promise<RewardDefinition | null> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));

    console.log("Rolls: ", rewardRolls)
    
    for (const roll of Object.values(rewardRolls)) {
      if (roll.roundId === roundId && roll.result?.hasWon) {
        return roll.result.reward || null;
      }
    }
    return null;
  }

  public async getRewardHistory(): Promise<RewardHistory> {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return rewardHistory;
  }
}

