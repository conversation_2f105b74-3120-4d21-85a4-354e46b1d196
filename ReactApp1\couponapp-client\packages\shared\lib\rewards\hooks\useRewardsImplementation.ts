import { useState, useCallback, useMemo } from 'react';
import { UseRewardsReturn, GameRoundResult, GameEndData, RewardLoadingState, RewardMechanics } from '../types';
import { RewardsManager } from '../RewardsManager';

export const useRewardsImplementation = (
  gameWidgetId: string,
  rewardMechanics: RewardMechanics[]
): UseRewardsReturn => {
  const [loadingState, setLoadingState] = useState<RewardLoadingState>({
    isLoading: false,
    operation: null,
  });

  // Create a new RewardsManager instance for this specific game widget
  const rewardsManager = useMemo(() => 
    new RewardsManager(gameWidgetId, rewardMechanics), 
    [gameWidgetId, rewardMechanics]
  );

  const roundStarted = useCallback((roundId: string): string => {
    // Trigger round start asynchronously without blocking
    rewardsManager.handleGameEvent('round_start', roundId).catch(error => {
      console.error('Error handling round start event:', error);
    });
    return roundId;
  }, [rewardsManager]);

  const roundFinished = useCallback(async (
    roundId: string,
    result: GameRoundResult
  ) => {
    setLoadingState({ isLoading: true, operation: 'rolling' });

    try {
      const rewardRoll = await rewardsManager.handleGameEvent('round_finish', roundId, result);
      return rewardRoll;
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  const gameFinished = useCallback(async (finalData: GameEndData) => {
    setLoadingState({ isLoading: true, operation: 'rolling' });

    try {
      const gameFinishRoundId = `game_finish_${Date.now()}`;
      await rewardsManager.handleGameEvent('game_finish', gameFinishRoundId, { score: finalData.score });
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  const getRewardByRoundId = useCallback(async (roundId: string) => {
    setLoadingState({ isLoading: true, operation: 'fetching' });
    
    try {
      return await rewardsManager.getRewardByRoundId(roundId);
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  const getRewardHistory = useCallback(async () => {
    setLoadingState({ isLoading: true, operation: 'fetching' });
    
    try {
      return await rewardsManager.getRewardHistory();
    } finally {
      setLoadingState({ isLoading: false, operation: null });
    }
  }, [rewardsManager]);

  return {
    rewardHistory: { rewardPoolId: gameWidgetId, rolls: [] },
    loadingState,
    roundStarted,
    roundFinished,
    gameFinished,
    getRewardByRoundId,
    getRewardHistory,
  };
};
